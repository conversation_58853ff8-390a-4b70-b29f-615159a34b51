package com.io661.extension.controller;

import com.io661.extension.model.Steam.ChangeOnSellStatusReq;
import com.io661.extension.model.Steam.ChangeOnSellStatusRes;
import com.io661.extension.model.Steam.SteamInventoryRes;
import com.io661.extension.model.YouPin.YouPinUserSellInventoryReq;
import com.io661.extension.model.YouPin.YouPinUserSellInventoryRes;
import com.io661.extension.service.Impl.PersonalSettingServiceImpl;
import com.io661.extension.service.Impl.TransactionAssistantServiceImpl;
import com.io661.extension.service.Impl.YouPinServiceImpl;
import com.io661.extension.service.PersonalSettingService;
import com.io661.extension.service.TransactionAssistantService;
import com.io661.extension.service.YouPinService;
import com.io661.extension.util.YouPin.YouPinCookieManager;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import lombok.Setter;

import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * 市场上架对话框控制器
 */
public class MarketListingDialogController implements Initializable {
    @FXML
    private Button closeButton;

    @FXML
    private Button cancelButton;

    @FXML
    private Button confirmButton;

    @FXML
    private CheckBox io661Checkbox;

    @FXML
    private CheckBox uuCheckbox;

    @FXML
    private CheckBox buffCheckbox;

    @FXML
    private Label priceHelpLabel;

    // 移除全局价格输入框，改为每个物品单独设置价格

    @FXML
    private VBox itemsContainer;

    private List<SteamInventoryRes> selectedItems = new ArrayList<>();
    private TransactionAssistantService transactionAssistantService;
    private YouPinService youPinService;
    private PersonalSettingService personalSettingService;

    // 是否是价格修改模式
    private boolean isPriceModifyMode = false;

    /**
     * 初始化方法
     */
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // 初始化服务
        transactionAssistantService = new TransactionAssistantServiceImpl();
        personalSettingService = new PersonalSettingServiceImpl();
        youPinService = new YouPinServiceImpl();

        // 设置确认按钮启用状态
        confirmButton.setDisable(false);
    }

    /**
     * 设置要上架或修改价格的物品
     *
     * @param items 物品列表
     * @param isPriceModify 是否是价格修改模式
     * @param confirmButtonText 确认按钮文本
     */
    public void setItems(List<SteamInventoryRes> items, boolean isPriceModify, String confirmButtonText) {
        this.selectedItems = items;
        this.isPriceModifyMode = isPriceModify;

        // 设置确认按钮文本
        if (confirmButton != null && confirmButtonText != null && !confirmButtonText.isEmpty()) {
            confirmButton.setText(confirmButtonText);
        }

        // 清空物品容器
        itemsContainer.getChildren().clear();

        // 添加物品到容器
        for (SteamInventoryRes item : items) {
            HBox itemRow = createItemRow(item);
            itemsContainer.getChildren().add(itemRow);
        }
    }

    /**
     * 设置要上架的物品（向后兼容）
     *
     * @param items 物品列表
     */
    public void setItems(List<SteamInventoryRes> items) {
        setItems(items, false, "确定上架");
    }

    /**
     * 创建物品行
     */
    private HBox createItemRow(SteamInventoryRes item) {
        HBox itemRow = new HBox();
        itemRow.setAlignment(Pos.CENTER_LEFT);
        itemRow.setSpacing(10);
        itemRow.setPadding(new Insets(5));
        itemRow.getStyleClass().add("item-row");

        // 物品图片
        ImageView itemImage = new ImageView();
        itemImage.setFitHeight(40);
        itemImage.setFitWidth(40);
        itemImage.setPreserveRatio(true);
        itemImage.getStyleClass().add("item-image");

        // 设置图片URL
        if (item.getIconUrl() != null && !item.getIconUrl().isEmpty()) {
            try {
                Image image = new Image(item.getIconUrl(), true);
                itemImage.setImage(image);
            } catch (Exception e) {
                System.out.println("图片加载异常: " + e.getMessage());
            }
        }

        // 物品名称和价格容器
        VBox itemInfo = new VBox();
        itemInfo.setSpacing(2);
        HBox.setHgrow(itemInfo, Priority.ALWAYS);

        // 物品名称
        Label nameLabel = new Label(item.getItemName() != null ? item.getItemName() : item.getHashName());
        nameLabel.getStyleClass().add("item-name");

        // 显示物品数量（如果是合并物品）
        Label quantityLabel = new Label();
        if (item.getQuantity() != null && item.getQuantity() > 1) {
            quantityLabel.setText("共 " + item.getQuantity() + " 件");
            quantityLabel.getStyleClass().add("item-quantity");
        } else {
            quantityLabel.setText("共 1 件");
            quantityLabel.getStyleClass().add("item-quantity");
        }

        // 参考价格
        Label priceLabel = new Label();
        if (item.getRefPrice() != null) {
            double priceInYuan = item.getRefPrice() / 100.0;
            DecimalFormat df = new DecimalFormat("0.00");
            priceLabel.setText("参考价: ¥" + df.format(priceInYuan));
        } else {
            priceLabel.setText("参考价: 暂无");
        }
        priceLabel.getStyleClass().add("item-price");

        itemInfo.getChildren().addAll(nameLabel, quantityLabel, priceLabel);

        // 创建价格输入区域
        VBox priceInputContainer = new VBox();
        priceInputContainer.setSpacing(5);

        // IO661价格输入区域
        HBox io661PriceContainer = new HBox();
        io661PriceContainer.setAlignment(Pos.CENTER_RIGHT);
        io661PriceContainer.setSpacing(5);

        Label io661ReceivedPriceLabel = new Label("IO661到手价:");
        io661ReceivedPriceLabel.getStyleClass().add("sell-price-label");

        TextField io661ReceivedPriceField = new TextField();
        io661ReceivedPriceField.getStyleClass().add("price-field");
        io661ReceivedPriceField.setPrefWidth(80);
        io661ReceivedPriceField.setPromptText("期望到手价");

        io661PriceContainer.getChildren().addAll(io661ReceivedPriceLabel, io661ReceivedPriceField);

        // 创建隐藏的上架价字段用于内部计算
        TextField io661ListingPriceField = new TextField();
        io661ListingPriceField.setVisible(false);
        io661ListingPriceField.setManaged(false);

        // 悠悠有品价格输入区域（默认隐藏）
        HBox uuPriceContainer = new HBox();
        uuPriceContainer.setAlignment(Pos.CENTER_RIGHT);
        uuPriceContainer.setSpacing(5);
        uuPriceContainer.setVisible(false);
        uuPriceContainer.setManaged(false);

        Label uuReceivedPriceLabel = new Label("UU到手价:");
        uuReceivedPriceLabel.getStyleClass().add("sell-price-label");

        TextField uuReceivedPriceField = new TextField();
        uuReceivedPriceField.getStyleClass().add("price-field");
        uuReceivedPriceField.setPrefWidth(80);
        uuReceivedPriceField.setPromptText("期望到手价");

        uuPriceContainer.getChildren().addAll(uuReceivedPriceLabel, uuReceivedPriceField);

        // 创建隐藏的上架价字段用于内部计算
        TextField uuListingPriceField = new TextField();
        uuListingPriceField.setVisible(false);
        uuListingPriceField.setManaged(false);

        priceInputContainer.getChildren().addAll(io661PriceContainer, uuPriceContainer);

        // 监听UU复选框状态变化
        uuCheckbox.selectedProperty().addListener((observable, oldValue, newValue) -> {
            uuPriceContainer.setVisible(newValue);
            uuPriceContainer.setManaged(newValue);
        });

        // 设置默认价格
        DecimalFormat df = new DecimalFormat("0.00");

        // 为IO661价格输入框设置默认价格（基于到手价）
        if (isPriceModifyMode && item.isOnSell() && item.getOnSellPrice() != null && item.getOnSellPrice() > 0) {
            // 修改价格模式：显示当前到手价
            int currentReceivedPrice = personalSettingService.calculateReceivedPrice("io661", item.getOnSellPrice());
            double receivedPriceInYuan = currentReceivedPrice / 100.0;
            io661ReceivedPriceField.setText(df.format(receivedPriceInYuan));
            uuReceivedPriceField.setText(df.format(receivedPriceInYuan));
        } else if (item.getRefPrice() != null) {
            // 上架模式：使用参考价格作为期望到手价
            double refPriceInYuan = item.getRefPrice() / 100.0;
            io661ReceivedPriceField.setText(df.format(refPriceInYuan));
            uuReceivedPriceField.setText(df.format(refPriceInYuan));
        }

        // 设置价格计算事件
        setupPriceCalculation(io661ReceivedPriceField, io661ListingPriceField, "io661", item, df);
        setupPriceCalculation(uuReceivedPriceField, uuListingPriceField, "youpin", item, df);

        // 将价格输入框存储在物品的用户数据中，以便后续获取
        // 存储一个Map，包含各平台的到手价和上架价输入框
        Map<String, Map<String, TextField>> priceFields = new HashMap<>();
        Map<String, TextField> io661Fields = new HashMap<>();
        io661Fields.put("received", io661ReceivedPriceField);
        io661Fields.put("listing", io661ListingPriceField);
        Map<String, TextField> uuFields = new HashMap<>();
        uuFields.put("received", uuReceivedPriceField);
        uuFields.put("listing", uuListingPriceField);
        priceFields.put("io661", io661Fields);
        priceFields.put("uu", uuFields);
        itemRow.setUserData(priceFields);

        // 添加到行
        itemRow.getChildren().addAll(itemImage, itemInfo, priceInputContainer);

        return itemRow;
    }

    /**
     * 设置价格计算事件处理
     */
    private void setupPriceCalculation(TextField receivedPriceField, TextField listingPriceField, String platform, SteamInventoryRes item, DecimalFormat df) {
        // 到手价输入框事件
        receivedPriceField.setOnMouseClicked(event -> receivedPriceField.selectAll());

        // 限制输入格式
        receivedPriceField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*(\\.\\d{0,2})?")) {
                receivedPriceField.setText(oldValue);
            }
        });

        // 到手价变化时自动计算上架价
        receivedPriceField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue != null && !newValue.isEmpty()) {
                try {
                    double receivedPrice = Double.parseDouble(newValue);
                    if (receivedPrice > 0) {
                        int receivedPriceInCents = (int) (receivedPrice * 100);
                        int listingPriceInCents = personalSettingService.calculateListingPrice(platform, receivedPriceInCents);
                        double listingPriceInYuan = listingPriceInCents / 100.0;
                        listingPriceField.setText(df.format(listingPriceInYuan));
                    } else {
                        listingPriceField.setText("");
                    }
                } catch (NumberFormatException e) {
                    listingPriceField.setText("");
                }
            } else {
                listingPriceField.setText("");
            }
        });

        // 焦点事件处理
        receivedPriceField.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue) { // 获得焦点
                Platform.runLater(receivedPriceField::selectAll);
            } else { // 失去焦点时格式化
                try {
                    if (!receivedPriceField.getText().isEmpty()) {
                        double price = Double.parseDouble(receivedPriceField.getText());
                        if (price > 0) {
                            receivedPriceField.setText(df.format(price));
                        } else {
                            // 设置为默认参考价格
                            if (item.getRefPrice() != null) {
                                double defaultPrice = item.getRefPrice() / 100.0;
                                receivedPriceField.setText(df.format(defaultPrice));
                            } else {
                                receivedPriceField.setText("0.01");
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    if (item.getRefPrice() != null) {
                        double defaultPrice = item.getRefPrice() / 100.0;
                        receivedPriceField.setText(df.format(defaultPrice));
                    } else {
                        receivedPriceField.setText("0.01");
                    }
                }
            }
        });
    }

    /**
     * 设置价格输入框的事件处理（保留原方法以兼容）
     */
    private void setupPriceField(TextField priceField, SteamInventoryRes item, DecimalFormat df) {
        // 触发一次焦点事件，确保用户输入的价格被记录
        priceField.setOnMouseClicked(event -> {
            priceField.selectAll();
        });

        // 设置价格输入框只允许输入数字和小数点，且限制小数点后两位
        priceField.textProperty().addListener((observable, oldValue, newValue) -> {
            // 检查是否是有效的价格格式（数字和小数点）
            if (!newValue.matches("\\d*(\\.\\d{0,2})?")) {
                // 如果不是有效格式，回退到上一个值
                priceField.setText(oldValue);
            }
        });

        // 设置焦点事件，当获得焦点时选中所有文本，方便用户直接输入新价格
        priceField.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue) { // 获得焦点
                Platform.runLater(priceField::selectAll);
            } else { // 失去焦点时格式化为两位小数
                try {
                    if (!priceField.getText().isEmpty()) {
                        double price = Double.parseDouble(priceField.getText());
                        // 确保价格大于0
                        if (price > 0) {
                            priceField.setText(df.format(price));
                        } else {
                            // 如果价格小于等于0，设置为默认参考价格
                            if (item.getRefPrice() != null) {
                                double defaultPrice = item.getRefPrice() / 100.0;
                                priceField.setText(df.format(defaultPrice));
                            } else {
                                priceField.setText("0.01"); // 最小价格
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    // 如果解析失败，设置为默认参考价格
                    if (item.getRefPrice() != null) {
                        double defaultPrice = item.getRefPrice() / 100.0;
                        priceField.setText(df.format(defaultPrice));
                    } else {
                        priceField.setText("0.01"); // 最小价格
                    }
                }
            }
        });
    }

    /**
     * 处理关闭按钮点击
     */
    @FXML
    private void handleClose() {
        closeDialog();
    }

    /**
     * 处理取消按钮点击
     */
    @FXML
    private void handleCancel() {
        closeDialog();
    }

    /**
     * -- SETTER --
     *  设置当前Steam账号ID
     */
    // 当前选中的Steam账号ID
    @Setter
    private String currentSteamId;

    /**
     * 处理确认按钮点击
     */
    @FXML
    private void handleConfirm() {
        // 获取YouPin token，如果没有currentSteamId或读取失败则为null
        String youPinToken = null;
        if (currentSteamId != null && !currentSteamId.isEmpty()) {
            try {
                youPinToken = YouPinCookieManager.readYouPinCookie(currentSteamId);
            } catch (Exception e) {
                System.out.println("读取YouPin Cookie失败: " + e.getMessage());
            }
        }

        // 如果没有currentSteamId，尝试从第一个物品获取steamId
        if (currentSteamId == null || currentSteamId.isEmpty()) {
            if (!selectedItems.isEmpty() && selectedItems.getFirst().getSteamId() != null) {
                currentSteamId = selectedItems.getFirst().getSteamId();
                System.out.println("从物品获取steamId: " + currentSteamId);
                try {
                    youPinToken = YouPinCookieManager.readYouPinCookie(currentSteamId);
                } catch (Exception e) {
                    System.out.println("读取YouPin Cookie失败: " + e.getMessage());
                }
            }
        }

        final String token = youPinToken;
        try {
            // 创建上架/修改价格请求
            ChangeOnSellStatusReq request = new ChangeOnSellStatusReq();
            List<ChangeOnSellStatusReq.Inventory> inventoryList = new ArrayList<>();

            YouPinUserSellInventoryReq inventoryYouPinList = new YouPinUserSellInventoryReq();


            if (isPriceModifyMode) {
                System.out.println("准备修改 " + selectedItems.size() + " 个物品的价格");
                System.out.println("注意：将使用用户输入的新价格，更新在售物品价格");
            } else {
                System.out.println("准备上架 " + selectedItems.size() + " 个物品");
                System.out.println("注意：将使用用户输入的价格，而非参考价格");
            }

            // 遍历物品容器中的所有行
            for (int i = 0; i < itemsContainer.getChildren().size(); i++) {
                HBox itemRow = (HBox) itemsContainer.getChildren().get(i);
                SteamInventoryRes item = selectedItems.get(i);
                String itemName = item.getItemName() != null ? item.getItemName() : item.getHashName();

                // 获取该行的价格输入框Map
                @SuppressWarnings("unchecked")
                Map<String, Map<String, TextField>> priceFields = (Map<String, Map<String, TextField>>) itemRow.getUserData();
                Map<String, TextField> io661Fields = priceFields.get("io661");
                Map<String, TextField> uuFields = priceFields.get("uu");

                TextField io661ListingPriceField = io661Fields.get("listing");
                TextField uuListingPriceField = uuFields.get("listing");

                String io661PriceText = io661ListingPriceField.getText();
                String uuPriceText = uuListingPriceField.getText();

                // 验证IO661价格
                if (io661PriceText == null || io661PriceText.isEmpty()) {
                    showAlert(Alert.AlertType.ERROR, "价格错误", "请为所有物品设置IO661到手价");
                    return;
                }

                // 验证悠悠有品价格（如果选中了悠悠有品）
                if (uuCheckbox.isSelected() && (uuPriceText == null || uuPriceText.isEmpty())) {
                    showAlert(Alert.AlertType.ERROR, "价格错误", "请为所有物品设置悠悠有品到手价");
                    return;
                }

                try {
                    // 处理IO661价格
                    double io661PriceInYuan = Double.parseDouble(io661PriceText);

                    // 验证IO661价格是否大于0
                    if (io661PriceInYuan <= 0) {
                        showAlert(Alert.AlertType.ERROR, "价格错误", "物品 " + itemName + " 的IO661价格必须大于0");
                        return;
                    }

                    // 处理悠悠有品价格
                    double uuPriceInYuan = 0;
                    if (uuCheckbox.isSelected()) {
                        uuPriceInYuan = Double.parseDouble(uuPriceText);
                        if (uuPriceInYuan <= 0) {
                            showAlert(Alert.AlertType.ERROR, "价格错误", "物品 " + itemName + " 的悠悠有品价格必须大于0");
                            return;
                        }
                    }

                    // 验证价格格式并格式化
                    DecimalFormat df = new DecimalFormat("0.00");
                    String[] io661Parts = io661PriceText.split("\\.");
                    if (io661Parts.length > 1 && io661Parts[1].length() > 2) {
                        io661PriceInYuan = Double.parseDouble(df.format(io661PriceInYuan));
                        io661PriceText = df.format(io661PriceInYuan);
                        io661ListingPriceField.setText(io661PriceText);
                    }

                    if (uuCheckbox.isSelected()) {
                        String[] uuParts = uuPriceText.split("\\.");
                        if (uuParts.length > 1 && uuParts[1].length() > 2) {
                            uuPriceInYuan = Double.parseDouble(df.format(uuPriceInYuan));
                            uuPriceText = df.format(uuPriceInYuan);
                            uuListingPriceField.setText(uuPriceText);
                        }
                    }

                    // IO661价格转换为分（Integer类型）
                    int io661PriceInCents = (int) (io661PriceInYuan * 100);

                    // 悠悠有品价格保持元格式（String类型）
                    if (uuCheckbox.isSelected()) {
                        uuPriceInYuan = Double.parseDouble(df.format(Double.parseDouble(uuPriceText)));
                    }

                    // 打印用户输入的价格，用于调试
                    System.out.println("用户输入的IO661价格: " + io661PriceText + " 元，转换为: " + io661PriceInCents + " 分");
                    if (uuCheckbox.isSelected()) {
                        System.out.println("用户输入的UU价格: " + uuPriceText + " 元，保持为: " + uuPriceInYuan + " 元");
                    }

                    // 处理合并物品和单个物品的情况
                    if (item.getIds() != null && !item.getIds().isEmpty()) {
                        // 合并物品：ids是逗号分隔的字符串，需要拆分
                        System.out.println("处理合并物品: " + itemName + ", IDs: " + item.getIds());
                        String[] idArray = item.getIds().split(",");
                        for (String id : idArray) {
                            if (id != null && !id.trim().isEmpty()) {
                                ChangeOnSellStatusReq.Inventory inventoryItem = new ChangeOnSellStatusReq.Inventory();
                                inventoryItem.setId(Long.parseLong(id.trim()));
                                inventoryItem.setPrice(io661PriceInCents);
                                inventoryList.add(inventoryItem);
                                System.out.println("  - 添加合并物品ID: " + id.trim() + ", 价格: " + io661PriceInYuan + "元");
                            }
                        }
                    } else if (item.getId() != null) {
                        // 单个物品：使用物品ID
                        System.out.println("处理单个物品: " + itemName + ", ID: " + item.getId());
                        ChangeOnSellStatusReq.Inventory inventoryItem = new ChangeOnSellStatusReq.Inventory();


                        YouPinUserSellInventoryReq.DataItem inventoryYouPinItemList = new YouPinUserSellInventoryReq.DataItem();
                        // 悠品上架

                        inventoryYouPinItemList.setAssetId(Long.valueOf(item.getAssetId()));
                        // 悠悠有品价格使用String类型，单位为元，不是分
                        inventoryYouPinItemList.setPrice(String.valueOf(uuPriceInYuan));
                        inventoryYouPinList.setData(List.of(inventoryYouPinItemList));


                        // IO661上架
                        inventoryItem.setId(item.getId());
                        inventoryItem.setPrice(io661PriceInCents);
                        inventoryList.add(inventoryItem);
                        System.out.println("  - 添加单个物品ID: " + item.getId() + ", IO661价格: " + io661PriceInYuan + "元");
                        if (uuCheckbox.isSelected()) {
                            System.out.println("  - UU价格: " + uuPriceInYuan + "元");
                        }
                    } else {
                        // 记录错误情况
                        System.out.println("警告：物品没有有效的ID：" + itemName);
                    }
                } catch (NumberFormatException e) {
                    showAlert(Alert.AlertType.ERROR, "价格错误", "物品 " + itemName + " 的价格格式无效");
                    return;
                }
            }

            // 如果没有有效的物品要上架，显示提示并返回
            if (inventoryList.isEmpty()) {
                showAlert(Alert.AlertType.WARNING, "上架提示", "没有有效的物品可以上架");
                return;
            }

            System.out.println("最终上架物品数量: " + inventoryList.size());
            request.setInventoryList(inventoryList);

            // 禁用确认按钮
            confirmButton.setDisable(true);
            confirmButton.setText("上架中...");

            // 在后台线程中发送请求
            new Thread(() -> {
                try {
                    // 发送IO661上架请求
                    ChangeOnSellStatusRes response = transactionAssistantService.changeOnSellStatus(request);

                    YouPinUserSellInventoryRes youPinUserSellInventoryRes = new YouPinUserSellInventoryRes();
                    // 如果选中悠悠有品上架
                    if (uuCheckbox.isSelected()) {
                        // 处理YouPin上架请求(assetIds,price)
                        youPinUserSellInventoryRes = youPinService.userSellInventory(token, inventoryYouPinList);


                        // 处理YouPin改价请求(Price, CommodityId)
                        youPinService.userItemsOnSellPriceChange(token, inventoryYouPinList);

                    }

                    // TODO: 为BUFF上架预留接口调用
                    // if (buffCheckbox.isSelected()) {
                    //     // 调用BUFF平台上架接口

                    //   }

                    // 在JavaFX线程中处理响应
                    YouPinUserSellInventoryRes finalYouPinUserSellInventoryRes = youPinUserSellInventoryRes;
                    Platform.runLater(() -> {
                        // 上架成功
                        int errorCount = response.getErrorList() != null ? response.getErrorList().size() : 0;
                        int successCount = inventoryList.size() - errorCount;

                        // 构建成功消息
                        StringBuilder message = new StringBuilder();

                        if (errorCount == 0) {
                            if (isPriceModifyMode) {
                                message.append("成功修改 ").append(successCount).append(" 件物品的价格");
                            } else {
                                message.append("IO661成功上架 ").append(successCount).append(" 件物品");
                            }

                            // 如果选中了悠悠有品，添加悠悠有品的结果
                            if (uuCheckbox.isSelected() && finalYouPinUserSellInventoryRes.getData() != null) {
                                int youPinSuccessCount = finalYouPinUserSellInventoryRes.getData().size();
                                message.append("\n悠悠有品成功上架 ").append(youPinSuccessCount).append(" 件物品");
                            }

                            showAlert(Alert.AlertType.INFORMATION, isPriceModifyMode ? "修改价格成功" : "上架成功", message.toString());
                            closeDialog();
                        } else {
                            if (isPriceModifyMode) {
                                message.append("成功修改 ").append(successCount).append(" 件物品的价格，失败 ").append(errorCount).append(" 件物品");
                            } else {
                                message.append("IO661成功上架 ").append(successCount).append(" 件物品，失败 ").append(errorCount).append(" 件物品");
                            }

                            // 如果选中了悠悠有品，添加悠悠有品的结果
                            if (uuCheckbox.isSelected() && finalYouPinUserSellInventoryRes.getData() != null) {
                                int youPinSuccessCount = finalYouPinUserSellInventoryRes.getData().size();
                                message.append("\n悠悠有品成功上架 ").append(youPinSuccessCount).append(" 件物品");
                            }

                            showAlert(Alert.AlertType.INFORMATION, isPriceModifyMode ? "修改价格部分成功" : "上架部分成功", message.toString());
                            closeDialog();
                        }
                    });
                } catch (Exception e) {
                    // 处理异常
                    Platform.runLater(() -> {
                        showAlert(Alert.AlertType.ERROR, "上架异常", e.getMessage());
                        confirmButton.setDisable(false);
                        confirmButton.setText("确定上架");
                    });
                }
            }).start();
        } catch (Exception e) {
            showAlert(Alert.AlertType.ERROR, "上架异常", "处理上架请求时发生错误: " + e.getMessage());
            confirmButton.setDisable(false);
            confirmButton.setText("确定上架");
        }
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) closeButton.getScene().getWindow();
        stage.close();
    }

    /**
     * 显示提示框
     */
    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }
}
